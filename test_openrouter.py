#!/usr/bin/env python3
"""
Test script để kiểm tra OpenRouter API key
"""
import requests
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_openrouter_direct():
    """Test OpenRouter API trực tiếp"""
    api_key = os.getenv("OPENROUTER_API_KEY")
    base_url = os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1")
    model = os.getenv("OPENROUTER_MODEL", "google/gemini-2.0-flash-001")
    site_url = os.getenv("OPENROUTER_SITE_URL")
    site_name = os.getenv("OPENROUTER_SITE_NAME")
    
    print(f"🔑 API Key: {api_key[:20]}..." if api_key else "❌ No API Key")
    print(f"🌐 Base URL: {base_url}")
    print(f"🤖 Model: {model}")
    print(f"🏠 Site URL: {site_url}")
    print(f"📝 Site Name: {site_name}")
    print()
    
    if not api_key:
        print("❌ OPENROUTER_API_KEY not found in environment")
        return False
    
    # Prepare headers
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    if site_url:
        headers["HTTP-Referer"] = site_url
    
    if site_name:
        headers["X-Title"] = site_name
    
    # Prepare request data
    data = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": "Viết một câu chào đơn giản bằng tiếng Việt."
            }
        ],
        "temperature": 0.1,
        "max_tokens": 100
    }
    
    try:
        print("🚀 Testing OpenRouter API...")
        response = requests.post(
            url=f"{base_url}/chat/completions",
            headers=headers,
            data=json.dumps(data),
            timeout=30
        )
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📋 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API call successful!")
            print(f"📝 Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ API call failed: {response.status_code}")
            print(f"📄 Error response: {response.text}")
            return False
            
    except Exception as e:
        print(f"💥 Exception occurred: {str(e)}")
        return False

def test_local_api():
    """Test local FastAPI endpoint"""
    print("\n" + "="*50)
    print("🧪 Testing Local FastAPI Endpoint")
    print("="*50)
    
    url = "http://localhost:8000/api/v1/exam/generate"
    data = {
        "lessons": [
            {
                "lesson_id": "test1",
                "yeu_cau_can_dat": "Test requirement",
                "muc_do": "Vận dụng"
            }
        ],
        "ten_truong": "Test School",
        "config": {
            "so_cau_trac_nghiem": 1,
            "so_cau_tu_luan": 1
        }
    }
    
    try:
        print("🚀 Testing local API...")
        response = requests.post(
            url=url,
            json=data,
            timeout=30
        )
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Local API call successful!")
            print(f"📝 Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ Local API call failed: {response.status_code}")
            print(f"📄 Error response: {response.text}")
            return False
            
    except Exception as e:
        print(f"💥 Exception occurred: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔍 OpenRouter API Test")
    print("="*50)
    
    # Test direct OpenRouter API
    direct_success = test_openrouter_direct()
    
    # Test local API
    local_success = test_local_api()
    
    print("\n" + "="*50)
    print("📊 Test Results")
    print("="*50)
    print(f"Direct OpenRouter API: {'✅ PASS' if direct_success else '❌ FAIL'}")
    print(f"Local FastAPI: {'✅ PASS' if local_success else '❌ FAIL'}")
